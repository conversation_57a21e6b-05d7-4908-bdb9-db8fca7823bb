"use client";

import GridProducts from "./GridProducts";
import LayoutHandler from "./LayoutHandler";
import { useEffect, useState, useCallback, useRef } from "react";
import { useSearchParams } from "next/navigation";
import { getProductList, getCategoryProductList } from "@/services/productList";
import { useFilter } from "../../contexts/FilterContext";

import Sidebar from "./Sidebar";
import FilterModal from "./FilterModal";

export default function Products2({
  initialData,
  initialFilters,
  pageType = 'products',
  categorySlug = null,
  initialLayout = 3
}) {
  const searchParams = useSearchParams();
  const {
    filters: currentFilters,
    updateFilters,
    parseUrlParams,
    hasActiveFilters: contextHasActiveFilters,
    clearFilters,
    validateAndUpdateFilters,
    isLoaded
  } = useFilter();

  // State management
  const [activeLayout, setActiveLayout] = useState(initialLayout);
  const [products, setProducts] = useState(initialData?.products || []);
  const [pagination, setPagination] = useState(initialData?.pagination || {});
  const [filterOptions] = useState(initialFilters || {});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Track if this is the initial load to prevent overriding localStorage filters
  const isInitialLoad = useRef(true);



  // Fetch new data with filters (client-side)
  const fetchDataWithFilters = useCallback(async (filters) => {
    setLoading(true);
    setError(null);

    try {
      // Build filter parameters for API call
      const apiFilters = {
        page: filters.page,
        limit: filters.limit,
        sort: filters.sort,
        categories: filters.categories,
        brands: filters.brands,
        sizes: filters.sizes,
        variantAttributes: filters.variantAttributes,
        priceMin: filters.priceMin,
        priceMax: filters.priceMax,
        inStock: filters.inStock,
        search: filters.search,
        pageType: pageType
      };

      // Remove empty/null values
      Object.keys(apiFilters).forEach(key => {
        if (apiFilters[key] === null || apiFilters[key] === undefined ||
          (Array.isArray(apiFilters[key]) && apiFilters[key].length === 0) ||
          (typeof apiFilters[key] === 'object' && Object.keys(apiFilters[key] || {}).length === 0)) {
          delete apiFilters[key];
        }
      });

      let response;
      if (pageType === 'category' && categorySlug) {
        response = await getCategoryProductList(categorySlug, apiFilters);
      } else {
        response = await getProductList(apiFilters);
      }
      if (response) {
        setProducts(response.products || []);
        setPagination(response.pagination || {});
      } else {
        throw new Error('Ürünler yüklenirken bir hata oluştu 2' + response?.success + "data: " + response?.data);
      }
    } catch (err) {
      console.error('Error fetching products:', err);
      setError(err.message || 'Ürünler yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [pageType, categorySlug]);

  // Navigate to new URL with filters and fetch data
  const navigateWithFilters = useCallback((filters) => {
    // Build URL params manually to avoid dependency issues
    const params = new URLSearchParams();

    if (filters.page > 1) params.set('page', filters.page.toString());
    if (filters.limit !== 12) params.set('limit', filters.limit.toString());
    if (filters.sort !== 'default') params.set('sort', filters.sort);
    if (filters.categories.length) params.set('categories', filters.categories.join(','));
    if (filters.brands.length) params.set('brands', filters.brands.join(','));
    if (filters.sizes.length) params.set('sizes', filters.sizes.join(','));

    // Handle variant attributes
    if (filters.variantAttributes) {
      Object.entries(filters.variantAttributes).forEach(([attrName, values]) => {
        if (values.length > 0) {
          params.set(`attr_${attrName}`, values.join(','));
        }
      });
    }

    if (filters.priceMin) params.set('price_min', filters.priceMin.toString());
    if (filters.priceMax) params.set('price_max', filters.priceMax.toString());
    if (filters.inStock !== null) params.set('in_stock', filters.inStock.toString());
    if (filters.search) params.set('search', filters.search);

    // Update URL without triggering navigation
    const newUrl = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`;
    window.history.pushState({}, '', newUrl);

    // Update context filters
    updateFilters(filters);

    // Fetch new data with updated filters
    fetchDataWithFilters(filters);
  }, [fetchDataWithFilters, updateFilters]);

  // Update filters and navigate
  const updateFiltersAndNavigate = useCallback((newFilters) => {
    const updatedFilters = { ...currentFilters, ...newFilters };
    navigateWithFilters(updatedFilters);
  }, [currentFilters, navigateWithFilters]);

  // Filter action functions
  const allProps = {
    // Current state
    products,
    pagination,
    filterOptions,
    loading,
    error,
    currentFilters,

    // Price filter
    setPrice: (priceRange) => {
      updateFiltersAndNavigate({
        priceMin: priceRange[0],
        priceMax: priceRange[1],
        page: 1
      });
    },

    // Size filter
    setSize: (size) => {
      const sizes = size === 'All' ? [] : [size];
      updateFiltersAndNavigate({
        sizes,
        page: 1
      });
    },

    // Availability filter
    setAvailability: (availability) => {
      const inStock = availability === 'All' ? null : availability;
      updateFiltersAndNavigate({
        inStock,
        page: 1
      });
    },

    // Brand filter
    setBrands: (brand) => {
      const brands = brand === 'All' ? [] : [brand];
      updateFiltersAndNavigate({
        brands,
        page: 1
      });
    },

    removeBrand: (brandToRemove) => {
      const brands = currentFilters.brands.filter(b => b !== brandToRemove);
      updateFiltersAndNavigate({
        brands,
        page: 1
      });
    },

    // Variant attribute filter
    setVariantAttribute: (attributeName, value) => {
      const currentValues = currentFilters.variantAttributes[attributeName] || [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];

      const newVariantAttributes = {
        ...currentFilters.variantAttributes,
        [attributeName]: newValues
      };

      // Remove empty arrays
      if (newValues.length === 0) {
        delete newVariantAttributes[attributeName];
      }

      updateFiltersAndNavigate({
        variantAttributes: newVariantAttributes,
        page: 1
      });
    },

    removeVariantAttribute: (attributeName, value) => {
      const currentValues = currentFilters.variantAttributes[attributeName] || [];
      const newValues = currentValues.filter(v => v !== value);

      const newVariantAttributes = {
        ...currentFilters.variantAttributes,
        [attributeName]: newValues
      };

      // Remove empty arrays
      if (newValues.length === 0) {
        delete newVariantAttributes[attributeName];
      }

      updateFiltersAndNavigate({
        variantAttributes: newVariantAttributes,
        page: 1
      });
    },
    // Sorting
    setSortingOption: (sort) => {
      updateFiltersAndNavigate({
        sort,
        page: 1
      });
    },

    // Pagination
    setCurrentPage: (page) => {
      updateFiltersAndNavigate({ page });
    },

    setItemPerPage: (limit) => {
      updateFiltersAndNavigate({
        limit,
        page: 1
      });
    },

    // Clear filters
    clearFilter: () => {
      const defaultFilters = {
        page: 1,
        limit: 12,
        sort: 'default',
        categories: [],
        brands: [],
        sizes: [],
        variantAttributes: {},
        priceMin: null,
        priceMax: null,
        inStock: null,
        search: ''
      };

      clearFilters();

      // Update URL
      const newUrl = window.location.pathname;
      window.history.pushState({}, '', newUrl);

      // Fetch new data
      fetchDataWithFilters(defaultFilters);
    }
  };

  // Update current filters when URL changes and fetch new data if needed
  useEffect(() => {
    if (isLoaded) {
      const hasUrlParams = searchParams.toString().length > 0;

      if (hasUrlParams) {
        // URL'de parametreler varsa, bunları kullan
        const newFilters = parseUrlParams(searchParams);
        updateFilters(newFilters);
      } else if (isInitialLoad.current) {
        // İlk yüklemede URL parametresi yoksa, FilterContext'ten gelen filtreleri kullan
        // (localStorage'dan yüklenen filtreler zaten FilterContext tarafından set edildi)
        isInitialLoad.current = false;
      }

      setLoading(false); // Reset loading state when new data arrives
    }
  }, [searchParams, isLoaded, parseUrlParams, updateFilters]);

  // Validate filters on initial load only
  useEffect(() => {
    if (isLoaded && filterOptions && Object.keys(filterOptions).length > 0) {
      // Sadece localStorage'dan gelen filtreleri validate et
      // Kategori değişimlerinde validasyon yapma
      const urlParams = new URLSearchParams(window.location.search);
      const hasUrlFilters = urlParams.toString().length > 0;

      if (hasUrlFilters) {
        // URL'de filtre varsa validate et
        const validatedFilters = validateAndUpdateFilters(filterOptions);

        // If filters were changed due to validation, update URL
        if (validatedFilters !== currentFilters) {
          const params = new URLSearchParams();

          if (validatedFilters.page > 1) params.set('page', validatedFilters.page.toString());
          if (validatedFilters.limit !== 12) params.set('limit', validatedFilters.limit.toString());
          if (validatedFilters.sort !== 'default') params.set('sort', validatedFilters.sort);
          if (validatedFilters.categories.length) params.set('categories', validatedFilters.categories.join(','));
          if (validatedFilters.brands.length) params.set('brands', validatedFilters.brands.join(','));
          if (validatedFilters.sizes.length) params.set('sizes', validatedFilters.sizes.join(','));

          // Handle variant attributes
          if (validatedFilters.variantAttributes) {
            Object.entries(validatedFilters.variantAttributes).forEach(([attrName, values]) => {
              if (values.length > 0) {
                params.set(`attr_${attrName}`, values.join(','));
              }
            });
          }

          if (validatedFilters.priceMin) params.set('price_min', validatedFilters.priceMin.toString());
          if (validatedFilters.priceMax) params.set('price_max', validatedFilters.priceMax.toString());
          if (validatedFilters.inStock !== null) params.set('in_stock', validatedFilters.inStock.toString());
          if (validatedFilters.search) params.set('search', validatedFilters.search);

          // Update URL without triggering navigation
          const newUrl = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`;
          window.history.replaceState({}, '', newUrl);
        }
      }
    }
  }, [isLoaded]); // Sadece isLoaded değiştiğinde çalış

  // Helper functions for display
  const getSortingDisplayText = (sort) => {
    switch (sort) {
      case 'price_asc': return 'Fiyat: Düşükten Yükseğe';
      case 'price_desc': return 'Fiyat: Yüksekten Düşüğe';
      case 'name_asc': return 'İsim: A-Z';
      case 'name_desc': return 'İsim: Z-A';
      case 'newest': return 'En Yeni';
      default: return 'Sıralama';
    }
  };

  const hasActiveFilters = contextHasActiveFilters;
  return (
    <>
      <section className="flat-spacing-24">
        <div className="container">
          <div className="row">
            <div className="col-xl-3">
              <div className="canvas-sidebar sidebar-filter canvas-filter left">
                <div className="canvas-wrapper">
                  <Sidebar allProps={allProps} />
                </div>
              </div>
            </div>
            <div className="col-xl-9">
              <div className="tf-shop-control">
                <div className="tf-group-filter">
                  <a
                    href="#filterShop"
                    data-bs-toggle="offcanvas"
                    aria-controls="filterShop"
                    className="tf-btn-filter d-flex d-xl-none"
                  >
                    <span className="icon icon-filter" />
                    <span className="text">Filtrele</span>
                  </a>
                  <div className="tf-dropdown-sort" data-bs-toggle="dropdown">
                    <div className="btn-select">
                      <span className="text-sort-value">{getSortingDisplayText(currentFilters.sort)}</span>
                      <span className="icon icon-arr-down" />
                    </div>
                    <div className="dropdown-menu">
                      {[
                        { value: 'default', label: 'Sıralama (Varsayılan)' },
                        { value: 'newest', label: 'En Yeni' },
                        { value: 'name_asc', label: 'İsim: A-Z' },
                        { value: 'name_desc', label: 'İsim: Z-A' },
                        { value: 'price_asc', label: 'Fiyat: Düşükten Yükseğe' },
                        { value: 'price_desc', label: 'Fiyat: Yüksekten Düşüğe' }
                      ].map((option, i) => (
                        <div
                          key={i}
                          className={`select-item ${currentFilters.sort === option.value ? "active" : ""}`}
                          onClick={() => allProps.setSortingOption(option.value)}
                        >
                          <span className="text-value-item">{option.label}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
                <ul className="tf-control-layout">
                  <LayoutHandler setActiveLayout={setActiveLayout} activeLayout={activeLayout} />
                </ul>
              </div>
              <div className="wrapper-control-shop">
                {/* Loading state */}
                {loading && (
                  <div className="loading-state text-center py-4">
                    <div className="spinner-border" role="status">
                      <span className="visually-hidden">Yükleniyor...</span>
                    </div>
                  </div>
                )}

                {/* Error state */}
                {error && (
                  <div className="alert alert-danger" role="alert">
                    {error}
                  </div>
                )}

                {/* Filter display and product count */}
                {hasActiveFilters() && (
                  <div className="meta-filter-shop">
                    <div id="product-count-grid" className="count-text">
                      <span className="count">{pagination.totalItems || products.length}</span> Ürün bulundu
                    </div>

                    <div id="applied-filters">
                      {/* Stock filter */}
                      {currentFilters.inStock !== null && (
                        <span className="filter-tag" onClick={() => allProps.setAvailability('All')}>
                          <span className="remove-tag icon-close"></span>
                          Stok: {currentFilters.inStock ? "Stokta Var" : "Stokta Yok"}
                        </span>
                      )}

                      {/* Brand filters */}
                      {currentFilters.brands.map((brand, index) => (
                        <span key={index} className="filter-tag" onClick={() => allProps.removeBrand(brand)}>
                          <span className="remove-tag icon-close" />
                          Marka: {brand}
                        </span>
                      ))}

                      {/* Price filter */}
                      {(currentFilters.priceMin !== null || currentFilters.priceMax !== null) && (
                        <span className="filter-tag" onClick={() => allProps.setPrice([null, null])}>
                          <span className="remove-tag icon-close" />
                          Fiyat: {currentFilters.priceMin || 0}₺ - {currentFilters.priceMax || '∞'}₺
                        </span>
                      )}

                      {/* Size filters */}
                      {currentFilters.sizes.map((size, index) => (
                        <span key={index} className="filter-tag" onClick={() => allProps.setSize('All')}>
                          <span className="remove-tag icon-close" />
                          Boyut: {size}
                        </span>
                      ))}

                      {/* Variant attribute filters */}
                      {Object.entries(currentFilters.variantAttributes || {}).map(([attrName, values]) =>
                        values.map((value, index) => (
                          <span
                            key={`${attrName}-${index}`}
                            className="filter-tag"
                            onClick={() => allProps.removeVariantAttribute(attrName, value)}
                          >
                            <span className="remove-tag icon-close" />
                            {attrName}: {value}
                          </span>
                        ))
                      )}

                      {/* Search filter */}
                      {currentFilters.search && (
                        <span className="filter-tag" onClick={() => allProps.clearFilter()}>
                          <span className="remove-tag icon-close" />
                          Arama: {currentFilters.search}
                        </span>
                      )}
                    </div>

                    <button id="remove-all" className="remove-all-filters" onClick={allProps.clearFilter}>
                      <i className="icon icon-close" /> Tüm filtreleri temizle
                    </button>
                  </div>
                )}
                {/* Product List */}
                {!loading && !error && products.length > 0 ? (
                  <>
                    <div
                      className={`wrapper-shop tf-grid-layout tf-col-${activeLayout}`}
                      id="gridLayout"
                    >
                      <GridProducts products={products} />
                    </div>

                    {/* Dynamic Pagination */}
                    {pagination && pagination.totalPages > 1 && (
                      <ul className="wg-pagination">
                        {/* Previous button */}
                        {pagination.currentPage > 1 && (
                          <li>
                            <a
                              href="#"
                              className="pagination-item"
                              onClick={(e) => {
                                e.preventDefault();
                                allProps.setCurrentPage(pagination.currentPage - 1);
                              }}
                            >
                              <i className="icon-arr-left2" />
                            </a>
                          </li>
                        )}

                        {/* Page numbers */}
                        {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                          let pageNum;
                          if (pagination.totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (pagination.currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (pagination.currentPage >= pagination.totalPages - 2) {
                            pageNum = pagination.totalPages - 4 + i;
                          } else {
                            pageNum = pagination.currentPage - 2 + i;
                          }

                          return (
                            <li key={pageNum} className={pagination.currentPage === pageNum ? "active" : ""}>
                              {pagination.currentPage === pageNum ? (
                                <div className="pagination-item">{pageNum}</div>
                              ) : (
                                <a
                                  href="#"
                                  className="pagination-item"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    allProps.setCurrentPage(pageNum);
                                  }}
                                >
                                  {pageNum}
                                </a>
                              )}
                            </li>
                          );
                        })}

                        {/* Next button */}
                        {pagination.currentPage < pagination.totalPages && (
                          <li>
                            <a
                              href="#"
                              className="pagination-item"
                              onClick={(e) => {
                                e.preventDefault();
                                allProps.setCurrentPage(pagination.currentPage + 1);
                              }}
                            >
                              <i className="icon-arr-right2" />
                            </a>
                          </li>
                        )}
                      </ul>
                    )}
                  </>
                ) : !loading && !error ? (
                  <div className="text-center py-5">
                    <h3>Ürün bulunamadı</h3>
                    <p>Aradığınız kriterlere uygun ürün bulunmuyor.</p>
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        </div>
      </section>
      <FilterModal allProps={allProps} />
    </>
  );
}
