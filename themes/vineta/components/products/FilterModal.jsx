"use client";
import React from "react";
import ProductFilters from "./ProductFilters";

export default function FilterModal({ allProps }) {
  return (
    <div
      className="offcanvas offcanvas-start canvas-sidebar canvas-filter"
      id="filterShop"
    >
      <div className="canvas-wrapper">
        <div className="canvas-header">
          <span className="title">Filtrele</span>
          <button
            className="icon-close icon-close-popup"
            data-bs-dismiss="offcanvas"
            aria-label="Kapat"
          />
        </div>
        <ProductFilters
          allProps={allProps}
          isMobile={true}
          showCategories={true}
        />
      </div>
    </div>
  );
}