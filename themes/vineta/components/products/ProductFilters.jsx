"use client";
import React from "react";
import Slider from "rc-slider";
import Link from "next/link";
import { useFilter } from "../../contexts/FilterContext";

export default function ProductFilters({
  allProps,
  isMobile = false,
  showCategories = true
}) {
  const { filterOptions, currentFilters } = allProps;
  const { closeFilterModal } = useFilter();

  // Extract filter options with fallbacks
  const categories = filterOptions?.categories || [];
  const availability = [
    { id: 1, value: null, label: 'Tümü', count: filterOptions?.availability?.totalCount || 0 },
    { id: 2, value: true, label: 'Stokta Var', count: filterOptions?.availability?.inStockCount || 0 },
    { id: 3, value: false, label: 'Stokta Yok', count: filterOptions?.availability?.outOfStockCount || 0 }
  ];
  const priceRange = {
    min: filterOptions?.priceRange?.minPrice || 0,
    max: filterOptions?.priceRange?.maxPrice || 1000
  };
  const brands = filterOptions?.brands || [];
  const variantAttributes = filterOptions?.variantAttributes || [];

  // Current price range for slider
  const currentPriceRange = [
    currentFilters.priceMin || priceRange.min,
    currentFilters.priceMax || priceRange.max
  ];

  return (
    <div className="canvas-body">
      {/* ---------- Kategoriler ---------- */}
      {showCategories && categories.length > 0 && (
        <div className="widget-facet">
          <div
            className="facet-title text-xl fw-medium"
            data-bs-target="#collections"
            data-bs-toggle="collapse"
            aria-expanded="true"
            aria-controls="collections"
          >
            <span>Kategoriler</span>
            <span className="icon icon-arrow-up" />
          </div>
          <div id="collections" className="collapse show">
            <ul className="collapse-body list-categories current-scrollbar">
              {categories.map((category) => (
                <li key={category.id} className="cate-item">
                  <Link
                    className="text-sm link"
                    href={`/urunler/${category.slug}`}
                    onClick={() => {
                      if (isMobile) {
                        closeFilterModal();
                      }
                    }}
                  >
                    <span>{category.name}</span>{" "}
                    <span className="count">({category.productCount || 0})</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* ---------- Stok Durumu ---------- */}
      <div className="widget-facet">
        <div
          className="facet-title text-xl fw-medium"
          data-bs-target="#availability"
          role="button"
          data-bs-toggle="collapse"
          aria-expanded="true"
          aria-controls="availability"
        >
          <span>Stok Durumu</span>
          <span className="icon icon-arrow-up" />
        </div>
        <div id="availability" className="collapse show">
          <ul className="collapse-body filter-group-check current-scrollbar">
            {availability.map((item) => (
              <li
                key={item.id}
                className="list-item"
                onClick={() => allProps.setAvailability(item.value)}
              >
                <input
                  type="radio"
                  className="tf-check"
                  readOnly
                  checked={currentFilters.inStock === item.value}
                />
                <label className="label">
                  <span>{item.label}</span>&nbsp;
                  <span className="count">({item.count})</span>
                </label>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* ---------- Fiyat ---------- */}
      <div className="widget-facet">
        <div
          className="facet-title text-xl fw-medium"
          data-bs-target="#price"
          role="button"
          data-bs-toggle="collapse"
          aria-expanded="true"
          aria-controls="price"
        >
          <span>Fiyat</span>
          <span className="icon icon-arrow-up" />
        </div>
        <div id="price" className="collapse show">
          <div className="collapse-body widget-price filter-price">
            <div
              className="price-val-range"
              id="price-value-range"
              data-min={priceRange.min}
              data-max={priceRange.max}
            >
              <Slider
                value={currentPriceRange}
                onChange={(price) => allProps.setPrice(price)}
                range
                min={priceRange.min}
                max={priceRange.max}
              />
            </div>
            <div className="box-value-price">
              <span className="text-sm">Fiyat:</span>
              <div className="price-box">
                <div
                  className="price-val"
                  id="price-min-value"
                  data-currency="₺"
                >
                  {currentPriceRange[0]}₺
                </div>
                <span>-</span>
                <div
                  className="price-val"
                  id="price-max-value"
                  data-currency="₺"
                >
                  {currentPriceRange[1]}₺
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* ---------- Marka ---------- */}
      {brands.length > 1 && (
        <div className="widget-facet">
          <div
            className="facet-title text-xl fw-medium"
            data-bs-target="#brand"
            role="button"
            data-bs-toggle="collapse"
            aria-expanded="true"
            aria-controls="brand"
          >
            <span>Marka</span>
            <span className="icon icon-arrow-up" />
          </div>
          <div id="brand" className="collapse show">
            <ul className="collapse-body filter-group-check current-scrollbar">
              {brands.map((brand) => (
                <li
                  key={brand.id || brand.name}
                  className="list-item"
                  onClick={() => allProps.setBrands(brand.name)}
                >
                  <input
                    type="checkbox"
                    className="tf-check"
                    readOnly
                    checked={currentFilters.brands.includes(brand.name)}
                  />
                  <label className="label">
                    <span>{brand.name}</span>&nbsp;
                    <span className="count">({brand.productCount || brand.count || 0})</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* ---------- Varyant Nitelikleri ---------- */}
      {variantAttributes.map((attribute) => (
        <div key={attribute.id} className="widget-facet">
          <div
            className="facet-title text-xl fw-medium"
            data-bs-target={`#attribute-${attribute.id}`}
            role="button"
            data-bs-toggle="collapse"
            aria-expanded="true"
            aria-controls={`attribute-${attribute.id}`}
          >
            <span>{attribute.name}</span>
            <span className="icon icon-arrow-up" />
          </div>
          <div id={`attribute-${attribute.id}`} className="collapse show">
            <ul className="collapse-body filter-group-check current-scrollbar">
              {attribute.values.map((value) => (
                <li
                  key={value.id}
                  className="list-item"
                  onClick={() => allProps.setVariantAttribute(attribute.shortName, value.value)}
                >
                  <input
                    type="checkbox"
                    className="tf-check"
                    readOnly
                    checked={
                      currentFilters.variantAttributes?.[attribute.shortName]?.includes(value.value) || false
                    }
                  />
                  <label className="label">
                    <span>{value.value}</span>&nbsp;
                    <span className="count">({value.productCount})</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>
        </div>
      ))}
    </div>
  );
}
