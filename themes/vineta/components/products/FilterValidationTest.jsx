"use client";
import React from 'react';
import { useFilter } from '../../contexts/FilterContext';

// Test component to demonstrate filter validation
export default function FilterValidationTest() {
  const { filters, validateAndUpdateFilters } = useFilter();

  // Simulate category change with different available options
  const testCategoryChange = () => {
    // Simulate new category with limited options
    const newCategoryOptions = {
      categories: [
        { id: 1, name: "Elektronik", slug: "elektronik" },
        { id: 2, name: "Giyi<PERSON>", slug: "giyim" }
      ],
      brands: [
        { name: "Apple" },
        { name: "Samsung" }
      ],
      variantAttributes: [
        {
          shortName: "renk",
          name: "<PERSON><PERSON>",
          values: [
            { value: "Siyah" },
            { value: "Beyaz" }
          ]
        }
      ],
      priceRange: {
        minPrice: 100,
        maxPrice: 5000
      }
    };

    console.log('Before validation:', filters);
    const validatedFilters = validateAndUpdateFilters(newCategoryOptions);
    console.log('After validation:', validatedFilters);
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
      <h3>Filter Validation Test</h3>
      <p>Current filters: {JSON.stringify(filters, null, 2)}</p>
      <button onClick={testCategoryChange}>
        Test Category Change (Validate Filters)
      </button>
    </div>
  );
}
